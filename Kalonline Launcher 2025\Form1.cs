﻿using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Windows.Forms;
using Ionic.Zip;

namespace Kalonline_Launcher_2025
{
    public partial class Form1 : Form
    {
        private string serverVersionUrl = "http://*************/version.txt";
        private string serverZipUrl = "http://*************/client.zip";
        private string localVersionPath = "version.txt";
        private string localZipPath = "update.zip";
        private string engineExe = "engine.exe";

        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                if (IsUpdateRequired())
                {
                    DownloadAndUpdate();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Update failed: " + ex.Message);
            }
        }

        private bool IsUpdateRequired()
        {
            string localVersion = File.Exists(localVersionPath) ? File.ReadAllText(localVersionPath).Trim() : "";
            string serverVersion = "";

            using (var client = new WebClient())
            {
                serverVersion = client.DownloadString(serverVersionUrl).Trim();
            }

            return !string.Equals(localVersion, serverVersion, StringComparison.OrdinalIgnoreCase);
        }

        private void DownloadAndUpdate()
        {
            using (var client = new WebClient())
            {
                client.DownloadFile(serverZipUrl, localZipPath);
            }

            // Extract zip to current directory using DotNetZip
            using (ZipFile zip = ZipFile.Read(localZipPath))
            {
                zip.ExtractAll(Application.StartupPath, ExtractExistingFileAction.OverwriteSilently);
            }

            File.Delete(localZipPath);
        }


        private void launchBTN_Click(object sender, EventArgs e)
        {
            if (File.Exists(engineExe))
            {
                Process.Start(engineExe);
                Application.Exit();
            }
            else
            {
                MessageBox.Show("engine.exe not found!");
            }
        }
    }
}